Page({
  data: {
    score: 0,
    timeLeft: 30,
    isPlaying: false,
    activeMoles: Array(9).fill(false),
    hitAnimations: Array(9).fill(false),
    scoreAnimations: Array(9).fill(false),
    combo: 0,
    lastHitScore: 10,
    gameStatus: '',
    consecutiveHits: 0,
    missedHits: 0
  },

  startGame: function() {
    // 播放开始音效
    this.playSound('start');

    this.setData({
      score: 0,
      timeLeft: 30,
      isPlaying: true,
      activeMoles: Array(9).fill(false),
      hitAnimations: Array(9).fill(false),
      scoreAnimations: Array(9).fill(false),
      combo: 0,
      lastHitScore: 10,
      gameStatus: '游戏开始！',
      consecutiveHits: 0,
      missedHits: 0
    });

    // 清除游戏状态提示
    setTimeout(() => {
      this.setData({ gameStatus: '' });
    }, 2000);

    this.gameTimer = setInterval(() => {
      if (this.data.timeLeft <= 1) {
        this.endGame();
      } else {
        this.setData({
          timeLeft: this.data.timeLeft - 1
        });

        // 时间警告特效
        if (this.data.timeLeft <= 10) {
          this.playSound('warning');
        }
      }
    }, 1000);

    this.showMoles();
  },

  endGame: function() {
    clearInterval(this.gameTimer);
    clearInterval(this.moleTimer);

    this.setData({
      isPlaying: false,
      timeLeft: 0,
      activeMoles: Array(9).fill(false),
      gameStatus: `游戏结束！最终得分：${this.data.score}`
    });

    // 播放结束音效
    this.playSound('end');

    // 震动反馈
    wx.vibrateShort();

    wx.showModal({
      title: '游戏结束',
      content: `恭喜你获得 ${this.data.score} 分！\n最高连击：${this.data.combo}`,
      showCancel: false,
      confirmText: '再来一局',
      success: (res) => {
        if (res.confirm) {
          this.startGame();
        }
      }
    });
  },

  showMoles: function() {
    this.moleTimer = setInterval(() => {
      if (!this.data.isPlaying) return;

      const activeMoles = [...this.data.activeMoles];

      // 随机隐藏地鼠
      for (let i = 0; i < activeMoles.length; i++) {
        if (activeMoles[i] && Math.random() < 0.6) {
          activeMoles[i] = false;
        }
      }

      // 随机显示地鼠（可能同时显示多个）
      const availableHoles = activeMoles.map((active, index) => !active ? index : -1).filter(index => index !== -1);
      const numMolesToShow = Math.min(Math.floor(Math.random() * 3) + 1, availableHoles.length);

      for (let i = 0; i < numMolesToShow; i++) {
        if (availableHoles.length > 0) {
          const randomIndex = Math.floor(Math.random() * availableHoles.length);
          const holeIndex = availableHoles.splice(randomIndex, 1)[0];
          activeMoles[holeIndex] = true;
        }
      }

      this.setData({ activeMoles });
    }, 1000 - Math.min(this.data.score * 10, 600)); // 游戏速度随分数增加
  },

  whackMole: function(e) {
    if (!this.data.isPlaying) return;

    const index = e.currentTarget.dataset.index;

    if (this.data.activeMoles[index]) {
      // 击中地鼠
      this.hitMole(index);
    } else {
      // 未击中
      this.missMole(index);
    }
  },

  hitMole: function(index) {
    // 计算得分（连击加成）
    const baseScore = 10;
    const comboBonus = Math.floor(this.data.combo / 3) * 5;
    const hitScore = baseScore + comboBonus;

    // 更新连击
    const newCombo = this.data.combo + 1;
    const consecutiveHits = this.data.consecutiveHits + 1;

    // 更新地鼠状态
    const activeMoles = [...this.data.activeMoles];
    activeMoles[index] = false;

    // 触发击中动画
    const hitAnimations = [...this.data.hitAnimations];
    const scoreAnimations = [...this.data.scoreAnimations];
    hitAnimations[index] = true;
    scoreAnimations[index] = true;

    this.setData({
      activeMoles,
      hitAnimations,
      scoreAnimations,
      score: this.data.score + hitScore,
      combo: newCombo,
      lastHitScore: hitScore,
      consecutiveHits,
      missedHits: 0
    });

    // 播放击中音效
    this.playSound('hit');

    // 震动反馈
    wx.vibrateShort();

    // 连击特效
    if (newCombo > 1 && newCombo % 5 === 0) {
      this.setData({ gameStatus: `超级连击 x${newCombo}！` });
      this.playSound('combo');
      setTimeout(() => {
        this.setData({ gameStatus: '' });
      }, 1500);
    }

    // 清除动画
    setTimeout(() => {
      const newHitAnimations = [...this.data.hitAnimations];
      const newScoreAnimations = [...this.data.scoreAnimations];
      newHitAnimations[index] = false;
      newScoreAnimations[index] = false;
      this.setData({
        hitAnimations: newHitAnimations,
        scoreAnimations: newScoreAnimations
      });
    }, 600);
  },

  missMole: function(index) {
    // 重置连击
    this.setData({
      combo: 0,
      missedHits: this.data.missedHits + 1
    });

    // 播放失误音效
    this.playSound('miss');
  },

  playSound: function(type) {
    // 由于微信小程序音频API限制，这里使用简单的提示音
    switch(type) {
      case 'hit':
        // 可以在这里添加音频播放逻辑
        break;
      case 'miss':
        // 失误音效
        break;
      case 'combo':
        // 连击音效
        break;
      case 'start':
        // 开始音效
        break;
      case 'end':
        // 结束音效
        break;
      case 'warning':
        // 警告音效
        break;
    }
  }
})