Page({
  data: {
    score: 0,
    timeLeft: 30,
    isPlaying: false,
    activeMoles: Array(9).fill(false)
  },

  startGame: function() {
    this.setData({
      score: 0,
      timeLeft: 30,
      isPlaying: true,
      activeMoles: Array(9).fill(false)
    });
    
    this.gameTimer = setInterval(() => {
      if (this.data.timeLeft <= 1) {
        clearInterval(this.gameTimer);
        clearInterval(this.moleTimer);
        this.setData({
          isPlaying: false,
          timeLeft: 0,
          activeMoles: Array(9).fill(false)
        });
        wx.showToast({
          title: '游戏结束！得分：' + this.data.score,
          icon: 'none',
          duration: 2000
        });
      } else {
        this.setData({
          timeLeft: this.data.timeLeft - 1
        });
      }
    }, 1000);
    
    this.showMoles();
  },
  
  showMoles: function() {
    this.moleTimer = setInterval(() => {
      if (!this.data.isPlaying) return;
      
      const activeMoles = [...this.data.activeMoles];
      
      // 随机隐藏一个地鼠
      for (let i = 0; i < activeMoles.length; i++) {
        if (activeMoles[i] && Math.random() < 0.5) {
          activeMoles[i] = false;
        }
      }
      
      // 随机显示一个地鼠
      const availableHoles = activeMoles.map((active, index) => !active ? index : -1).filter(index => index !== -1);
      if (availableHoles.length > 0) {
        const randomIndex = Math.floor(Math.random() * availableHoles.length);
        activeMoles[availableHoles[randomIndex]] = true;
      }
      
      this.setData({ activeMoles });
    }, 800);
  },
  
  whackMole: function(e) {
    if (!this.data.isPlaying) return;
    
    const index = e.currentTarget.dataset.index;
    if (this.data.activeMoles[index]) {
      const activeMoles = [...this.data.activeMoles];
      activeMoles[index] = false;
      
      this.setData({
        activeMoles,
        score: this.data.score + 10
      });
    }
  }
})