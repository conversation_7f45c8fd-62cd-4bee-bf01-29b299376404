<view class="container">
  <!-- 背景草坪 -->
  <view class="grass-background"></view>

  <!-- 得分和连击显示 -->
  <view class="score-board">
    <text class="score-text">得分: {{score}}</text>
    <text class="combo-text" wx:if="{{combo > 1}}">连击 x{{combo}}</text>
  </view>
  <view class="time-board">时间: {{timeLeft}}秒</view>

  <!-- 游戏区域 -->
  <view class="game-container">
    <view class="hole-row" wx:for="{{[0, 1, 2]}}" wx:for-item="row" wx:key="row">
      <view class="hole" wx:for="{{[0, 1, 2]}}" wx:for-item="col" wx:key="col" bindtap="whackMole" data-index="{{row * 3 + col}}">
        <!-- 地洞 -->
        <view class="hole-bg"></view>
        <!-- 地鼠 -->
        <image class="mole {{activeMoles[row * 3 + col] ? 'active' : ''}} {{hitAnimations[row * 3 + col] ? 'hit' : ''}}"
               src="mole.png"></image>

        <!-- 击中特效 -->
        <view class="hit-effect {{hitAnimations[row * 3 + col] ? 'show' : ''}}" wx:if="{{hitAnimations[row * 3 + col]}}">
          <view class="particle" wx:for="{{[1,2,3,4,5]}}" wx:key="*this"></view>
        </view>

        <!-- 得分飞出动画 -->
        <view class="score-popup {{scoreAnimations[row * 3 + col] ? 'show' : ''}}" wx:if="{{scoreAnimations[row * 3 + col]}}">
          +{{lastHitScore}}
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏状态提示 -->
  <view class="game-status" wx:if="{{gameStatus}}">{{gameStatus}}</view>

  <button class="start-btn {{isPlaying ? 'playing' : ''}}" bindtap="startGame" disabled="{{isPlaying}}">
    {{isPlaying ? '游戏进行中...' : '开始游戏'}}
  </button>
</view>
