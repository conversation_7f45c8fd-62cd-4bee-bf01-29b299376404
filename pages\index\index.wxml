<view class="container">
  <view class="score-board">得分: {{score}}</view>
  <view class="time-board">时间: {{timeLeft}}秒</view>
  
  <view class="game-container">
    <view class="hole-row" wx:for="{{[0, 1, 2]}}" wx:for-item="row" wx:key="row">
      <view class="hole" wx:for="{{[0, 1, 2]}}" wx:for-item="col" wx:key="col" bindtap="whackMole" data-index="{{row * 3 + col}}">
        <image class="mole {{activeMoles[row * 3 + col] ? 'active' : ''}}" src="../../images/mole.png"></image>
      </view>
    </view>
  </view>
  
  <button class="start-btn" bindtap="startGame" disabled="{{isPlaying}}">开始游戏</button>
</view>
