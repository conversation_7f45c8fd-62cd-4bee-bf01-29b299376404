/* 页面容器 */
.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 草坪背景 */
.grass-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 30%, #90EE90 30%, #228B22 100%);
  background-image:
    radial-gradient(circle at 20% 80%, #32CD32 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, #32CD32 1px, transparent 1px),
    radial-gradient(circle at 40% 40%, #228B22 1px, transparent 1px);
  background-size: 60px 60px, 40px 40px, 80px 80px;
  z-index: -1;
  animation: grassWave 3s ease-in-out infinite;
}

@keyframes grassWave {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

/* 得分板样式 */
.score-board {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.score-text {
  font-size: 20px;
  font-weight: bold;
  color: #2E8B57;
}

.combo-text {
  font-size: 16px;
  color: #FF6347;
  font-weight: bold;
  animation: comboGlow 0.5s ease-in-out infinite alternate;
}

@keyframes comboGlow {
  from { text-shadow: 0 0 5px #FF6347; }
  to { text-shadow: 0 0 15px #FF6347, 0 0 20px #FF6347; }
}

.time-board {
  font-size: 18px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 15px;
  color: #8B4513;
  font-weight: bold;
}

/* 游戏容器 */
.game-container {
  width: 320px;
  height: 320px;
  margin: 20px 0;
  background: rgba(139, 69, 19, 0.1);
  border-radius: 20px;
  padding: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.hole-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 10px;
}

/* 地洞样式 */
.hole {
  width: 90px;
  height: 90px;
  margin: 5px;
  position: relative;
  cursor: pointer;
}

.hole-bg {
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, #2F1B14 30%, #8B4513 70%);
  border-radius: 50%;
  box-shadow:
    inset 0 0 20px rgba(0, 0, 0, 0.8),
    0 4px 8px rgba(0, 0, 0, 0.3);
  border: 3px solid #654321;
}

/* 地鼠样式 */
.mole {
  width: 70px;
  height: 70px;
  position: absolute;
  bottom: -70px;
  left: 10px;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  z-index: 2;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.mole.active {
  bottom: 10px;
  animation: molePopUp 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.mole.hit {
  animation: moleHit 0.3s ease-out;
}

@keyframes molePopUp {
  0% {
    bottom: -70px;
    transform: scale(0.8) rotate(-5deg);
  }
  50% {
    bottom: 15px;
    transform: scale(1.1) rotate(2deg);
  }
  100% {
    bottom: 10px;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes moleHit {
  0% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(0.8) rotate(-10deg); }
  100% { transform: scale(0.6) rotate(5deg); opacity: 0; }
}

/* 击中特效 */
.hit-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 3;
}

.hit-effect.show {
  animation: hitEffectShow 0.6s ease-out;
}

.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #FFD700;
  border-radius: 50%;
  animation: particleExplode 0.6s ease-out forwards;
}

.particle:nth-child(1) { animation-delay: 0s; transform: rotate(0deg); }
.particle:nth-child(2) { animation-delay: 0.1s; transform: rotate(72deg); }
.particle:nth-child(3) { animation-delay: 0.2s; transform: rotate(144deg); }
.particle:nth-child(4) { animation-delay: 0.3s; transform: rotate(216deg); }
.particle:nth-child(5) { animation-delay: 0.4s; transform: rotate(288deg); }

@keyframes particleExplode {
  0% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(30px) scale(0);
    opacity: 0;
  }
}

/* 得分飞出动画 */
.score-popup {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: #FFD700;
  font-size: 20px;
  font-weight: bold;
  pointer-events: none;
  z-index: 4;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.score-popup.show {
  animation: scorePopup 1s ease-out forwards;
}

@keyframes scorePopup {
  0% {
    transform: translateX(-50%) translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50px) scale(1.5);
    opacity: 0;
  }
}

/* 游戏状态提示 */
.game-status {
  font-size: 24px;
  font-weight: bold;
  color: #FF6347;
  margin: 10px 0;
  text-align: center;
  animation: statusPulse 1s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 开始按钮 */
.start-btn {
  margin-top: 20px;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.start-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.start-btn.playing {
  background: linear-gradient(45deg, #FF6347, #FF4500);
  animation: buttonPulse 2s ease-in-out infinite;
}

@keyframes buttonPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}