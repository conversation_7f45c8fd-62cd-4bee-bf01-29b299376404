.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-board, .time-board {
  font-size: 18px;
  margin-bottom: 10px;
}

.game-container {
  width: 300px;
  height: 300px;
  margin: 20px 0;
}

.hole-row {
  display: flex;
  justify-content: space-around;
}

.hole {
  width: 80px;
  height: 80px;
  margin: 5px;
  background-color: #8B4513;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.mole {
  width: 70px;
  height: 70px;
  position: absolute;
  bottom: -70px;
  left: 5px;
  transition: bottom 0.3s;
}

.mole.active {
  bottom: 0;
}

.start-btn {
  margin-top: 20px;
  background-color: #4CAF50;
  color: white;
}